import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { donViSearchColumns } from '@/constants/search-columns';
import QUERY_KEYS from '@/constants/query-keys';
import { DonViChiNhanh } from '@/types/schemas';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  selectedDonVi?: DonViChiNhanh | null;
  setSelectedDonVi?: (donVi: DonViChiNhanh) => void;
  onSetKy?: (ky: number) => void;
  onSetNam?: (nam: number) => void;
  ky?: number;
  nam?: number;
}

const BasicInfo: React.FC<BasicInfoProps> = ({
  selectedDonVi,
  setSelectedDonVi,
  onSetKy,
  onSetNam,
  ky,
  nam
}: BasicInfoProps) => {
  return (
    <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
      <div className='space-y-2 p-4'>
        <div className='flex flex-col space-y-3'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Kỳ</Label>
            <FormField name='ky' label='' type='number' value={ky} onValueChange={onSetKy} className='w-64' />
          </div>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Năm</Label>
            <FormField name='nam' label='' type='number' value={nam} onValueChange={onSetNam} className='w-64' />
          </div>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Đơn vị</Label>
            <div className='flex items-center gap-2'>
              <SearchField<DonViChiNhanh>
                type='text'
                displayRelatedField='ten_unit'
                columnDisplay='ma_unit'
                searchEndpoint={`/${QUERY_KEYS.DON_VI_CHI_NHANH}/`}
                searchColumns={donViSearchColumns}
                value={selectedDonVi?.ma_unit || ''}
                onValueChange={setSelectedDonVi}
                dialogTitle='Danh mục đơn vị'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
