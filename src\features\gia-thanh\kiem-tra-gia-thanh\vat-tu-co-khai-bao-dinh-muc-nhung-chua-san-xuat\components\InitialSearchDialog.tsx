import React, { useState } from 'react';

import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { DonViChiNhanh } from '@/types/schemas';
import { searchSchema } from '../schema';
import BasicInfo from './BasicInfo';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
  onSetSubTitle: (subTitle: string) => void;
  onSetDonViChiNhanh: (donViChiNhanh: DonViChiNhanh | null) => void;
  onSetKy: (ky: number) => void;
  onSetNam: (nam: number) => void;
  ky: number;
  nam: number;
  donViChiNhanh: DonViChiNhanh | null;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({
  open,
  onClose,
  onSearch,
  onSetSubTitle,
  onSetDonViChiNhanh,
  onSetKy,
  onSetNam,
  ky,
  nam,
  donViChiNhanh
}) => {
  const handleSubmit = (data: any) => {
    // Create subtitle for ActionBar display
    const donViInfo = donViChiNhanh ? `[${donViChiNhanh.ma_unit} - ${donViChiNhanh.ten_unit}]` : '';
    const subTitle = `Đơn vị: ${donViInfo} từ kỳ ${ky} năm ${nam} đến kỳ ${ky} năm ${nam}`;

    onSetSubTitle(subTitle);
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Vật tư có khai báo định mức nhưng chưa sản xuất'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={data => {
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <BasicInfo
            selectedDonVi={donViChiNhanh}
            setSelectedDonVi={onSetDonViChiNhanh}
            onSetKy={onSetKy}
            onSetNam={onSetNam}
            ky={ky}
            nam={nam}
          />
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <>
            <div className='space-x-2 p-2'>
              <Button className='!hover:bg-main-light bg-main' type='submit' variant='contained'>
                <AritoIcon icon={884} marginX='4px' />
                Đồng ý
              </Button>

              <Button onClick={() => onClose()} variant='outlined'>
                <AritoIcon icon={885} marginX='4px' />
                Huỷ
              </Button>
            </div>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
