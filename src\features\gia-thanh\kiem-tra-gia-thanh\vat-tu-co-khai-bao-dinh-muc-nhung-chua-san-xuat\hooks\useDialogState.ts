import { useState } from 'react';
import { DonViChiNhanh } from '@/types/schemas';

export interface SearchParams {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showTable: boolean;
  subTitle: string;
  donViChiNhanh: DonViChiNhanh | null;
  ky: number;
  nam: number;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchParams) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  setShowTable: (show: boolean) => void;
  setSubTitle: (subTitle: string) => void;
  setDonViChiNhanh: (donViChiNhanh: DonViChiNhanh | null) => void;
  setKy: (ky: number) => void;
  setNam: (nam: number) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [subTitle, setSubTitle] = useState('');
  const [donViChiNhanh, setDonViChiNhanh] = useState<DonViChiNhanh | null>(null);
  const [ky, setKy] = useState(new Date().getMonth() + 1);
  const [nam, setNam] = useState(new Date().getFullYear());

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchParams) => {
    // Show the table and close the dialog
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    // This handler is kept for future implementation
    console.log('Edit print template clicked');
  };

  return {
    initialSearchDialogOpen,
    showTable,
    subTitle,
    donViChiNhanh,
    ky,
    nam,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    setShowTable,
    setSubTitle,
    setDonViChiNhanh,
    setKy,
    setNam
  };
}
